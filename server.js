const WebSocket = require('ws');

// 建立 WebSocket 伺服器
const wss = new WebSocket.Server({ port: 8080 });

console.log('WebSocket 伺服器運行在 ws://localhost:8080');

// 模擬設備資料
const devices = [
  { id: 'device_01', name: '馬達 1', position: { x: -2, y: 1, z: 0 } },
  { id: 'device_02', name: '馬達 2', position: { x: 2, y: 1, z: 0 } },
  { id: 'device_03', name: '感測器 1', position: { x: 0, y: -1, z: 0 } }
];

// 當客戶端連接時
wss.on('connection', function connection(ws) {
  console.log('新的客戶端連接');

  // 每秒發送模擬資料
  const interval = setInterval(() => {
    const forceData = devices.map((device, index) => ({
      deviceId: device.id,
      deviceName: device.name,
      position: device.position,
      force: (Math.random() * 10 + 1).toFixed(2), // 1-11 N
      direction: {
        x: Math.random() > 0.5 ? 1 : -1,
        y: Math.random() > 0.5 ? 1 : -1,
        z: 0
      },
      timestamp: new Date().toISOString(),
      color: [0xff0000, 0x00ff00, 0x0000ff][index] // 紅、綠、藍
    }));

    // 發送資料給所有連接的客戶端
    const message = JSON.stringify({
      type: 'force_update',
      data: forceData
    });

    wss.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });

    console.log(`發送力值資料: ${forceData.map(d => `${d.deviceName}: ${d.force}N`).join(', ')}`);
  }, 1000);

  // 當客戶端斷線時
  ws.on('close', () => {
    console.log('客戶端斷線');
    clearInterval(interval);
  });

  // 發送歡迎訊息
  ws.send(JSON.stringify({
    type: 'welcome',
    message: '歡迎連接到力值監控系統',
    devices: devices
  }));
});

// 處理伺服器錯誤
wss.on('error', (error) => {
  console.error('WebSocket 伺服器錯誤:', error);
});

// 優雅關閉
process.on('SIGINT', () => {
  console.log('\n正在關閉 WebSocket 伺服器...');
  wss.close(() => {
    console.log('WebSocket 伺服器已關閉');
    process.exit(0);
  });
});
