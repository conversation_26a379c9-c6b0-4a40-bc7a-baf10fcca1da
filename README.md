# 力的分散顯示系統

這是一個基於 React + Three.js 開發的即時力值監控與視覺化系統，可以連接多個設備並即時顯示力的大小與方向。

## 功能特色

- 🎯 **即時力值顯示** - 透過 WebSocket 即時接收設備資料
- 🎨 **自訂背景圖片** - 支援上傳自訂的設備圖片作為背景
- 📊 **3D 視覺化** - 使用 Three.js 呈現立體的力向量箭頭
- 🔗 **多設備支援** - 同時監控多個力感測器或馬達
- 💻 **跨平台** - 純 Web 技術，支援各種瀏覽器

## 技術架構

- **前端**: React + Three.js
- **即時通訊**: WebSocket
- **後端**: Node.js (模擬伺服器)
- **視覺化**: 3D 箭頭動畫顯示力的方向與大小

## 快速開始

### 安裝依賴

```bash
npm install
```

### 啟動 WebSocket 模擬伺服器

在一個終端機中執行：

```bash
node server.js
```

這會啟動一個 WebSocket 伺服器在 `ws://localhost:8080`，模擬三個設備的力值資料。

### 啟動前端應用

在另一個終端機中執行：

```bash
npm start
```

開啟瀏覽器訪問 [http://localhost:3000](http://localhost:3000) 即可看到力的分散顯示系統。

## 可用指令

### `npm start`

在開發模式下執行應用程式。\
在瀏覽器中開啟 [http://localhost:3000](http://localhost:3000) 查看。

當你修改程式碼時，頁面會自動重新載入。\
你也可以在控制台中看到任何 lint 錯誤。

### `npm test`

在互動式監視模式下啟動測試執行器。\
查看 [執行測試](https://facebook.github.io/create-react-app/docs/running-tests) 章節以獲得更多資訊。

### `npm run build`

將應用程式建置到 `build` 資料夾以供正式環境使用。\
它會正確地將 React 打包為正式環境模式，並優化建置以獲得最佳效能。

建置檔案會被壓縮，檔案名稱包含雜湊值。\
你的應用程式已準備好部署！

查看 [部署](https://facebook.github.io/create-react-app/docs/deployment) 章節以獲得更多資訊。

### `npm run eject`

**注意：這是一個單向操作。一旦你執行 `eject`，就無法回復！**

如果你對建置工具和設定選擇不滿意，你可以隨時執行 `eject`。這個指令會從你的專案中移除單一建置依賴。

相反地，它會將所有設定檔案和傳遞依賴（webpack、Babel、ESLint 等）直接複製到你的專案中，讓你完全控制它們。除了 `eject` 之外的所有指令仍然有效，但它們會指向複製的腳本，讓你可以調整它們。此時你就要自己負責了。

你不必使用 `eject`。精選的功能集適合小型和中型部署，你不應該覺得有義務使用此功能。但是我們理解，如果你無法在準備好時自訂它，這個工具就沒有用處。

## 使用說明

### 上傳背景圖片

1. 點擊左上角控制面板中的「背景圖片」檔案選擇器
2. 選擇你的設備圖片（支援 JPG、PNG 等格式）
3. 圖片會自動設定為 3D 場景的背景

### 查看即時資料

- 左上角面板會顯示所有連接設備的即時力值
- 每個設備都有不同顏色的箭頭表示：
  - 🔴 紅色：設備 1 (馬達 1)
  - 🟢 綠色：設備 2 (馬達 2)
  - 🔵 藍色：設備 3 (感測器 1)

### WebSocket 資料格式

如果你要連接真實設備，WebSocket 訊息格式如下：

```json
{
  "type": "force_update",
  "data": [
    {
      "deviceId": "device_01",
      "deviceName": "馬達 1",
      "position": { "x": -2, "y": 1, "z": 0 },
      "force": "8.5",
      "direction": { "x": 1, "y": 0, "z": 0 },
      "timestamp": "2024-01-01T12:00:00.000Z",
      "color": 16711680
    }
  ]
}
```

## 專案結構

```
force-display-demo/
├── public/
│   ├── machine.svg          # 預設背景圖片
│   └── index.html
├── src/
│   ├── ForceDisplay.js      # 主要的 3D 顯示組件
│   ├── App.js              # 應用程式入口
│   └── index.js
├── server.js               # WebSocket 模擬伺服器
└── package.json
```

## 進階功能

### 自訂設備位置

在 `ForceDisplay.js` 中修改 `createInitialArrows()` 函數來調整箭頭位置：

```javascript
const positions = [
  { x: -2, y: 1, z: 0 },  // 設備 1 位置
  { x: 2, y: 1, z: 0 },   // 設備 2 位置
  { x: 0, y: -1, z: 0 }   // 設備 3 位置
];
```

### 連接真實設備

1. 修改 WebSocket 連接位址（目前為 `ws://localhost:8080`）
2. 確保你的設備發送符合格式的 JSON 資料
3. 調整力值的縮放比例以符合你的需求

## 了解更多

你可以在以下資源中了解更多：

- [Create React App 文件](https://facebook.github.io/create-react-app/docs/getting-started)
- [React 文件](https://reactjs.org/)
- [Three.js 文件](https://threejs.org/docs/)

### 程式碼分割

此章節已移至：[https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### 分析套件大小

此章節已移至：[https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### 製作漸進式網頁應用程式

此章節已移至：[https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### 進階設定

此章節已移至：[https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### 部署

此章節已移至：[https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm run build` 壓縮失敗

此章節已移至：[https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)

## 授權

此專案使用 MIT 授權。

## 貢獻

歡迎提交 Issue 和 Pull Request 來改善這個專案！
