import React, { useEffect, useRef, useState } from "react";
import * as THREE from "three";

export default function ForceDisplay() {
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const cameraRef = useRef(null);
  const arrowsRef = useRef([]);
  const [forceData, setForceData] = useState([]);
  const [backgroundImage, setBackgroundImage] = useState(null);

  useEffect(() => {
    // === Three.js 初始化 ===
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // 相機
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    camera.position.z = 5;
    cameraRef.current = camera;

    // 渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0.1); // 半透明背景
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // 光源
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 5, 5);
    scene.add(directionalLight);

    // 建立初始的力箭頭
    createInitialArrows();

    // 渲染循環
    const animate = function () {
      requestAnimationFrame(animate);
      
      // 旋轉箭頭以增加動態效果
      arrowsRef.current.forEach((arrow, index) => {
        if (arrow) {
          arrow.rotation.z += 0.01 * (index + 1);
        }
      });
      
      renderer.render(scene, camera);
    };
    animate();

    // WebSocket 連接
    let ws = null;
    try {
      ws = new WebSocket('ws://localhost:8080');

      ws.onopen = () => {
        console.log('WebSocket 連接成功');
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);

          if (message.type === 'force_update') {
            const newForceData = message.data.map((item, index) => ({
              id: index + 1,
              deviceId: item.deviceId,
              deviceName: item.deviceName,
              position: item.position,
              force: parseFloat(item.force),
              direction: item.direction,
              color: item.color,
              timestamp: item.timestamp
            }));

            setForceData(newForceData);
            updateArrows(newForceData);
          }
        } catch (error) {
          console.error('解析 WebSocket 訊息錯誤:', error);
        }
      };

      ws.onclose = () => {
        console.log('WebSocket 連接關閉');
      };

      ws.onerror = (error) => {
        console.error('WebSocket 錯誤:', error);
      };
    } catch (error) {
      console.error('WebSocket 連接失敗:', error);
      // 如果 WebSocket 連接失敗，使用模擬資料
      const interval = setInterval(() => {
        const newForceData = [
          {
            id: 1,
            deviceName: '模擬設備 1',
            position: { x: -2, y: 1, z: 0 },
            force: Math.random() * 5 + 1,
            direction: { x: 1, y: 0, z: 0 },
            color: 0xff0000
          },
          {
            id: 2,
            deviceName: '模擬設備 2',
            position: { x: 2, y: 1, z: 0 },
            force: Math.random() * 5 + 1,
            direction: { x: -1, y: 0, z: 0 },
            color: 0x00ff00
          },
          {
            id: 3,
            deviceName: '模擬設備 3',
            position: { x: 0, y: -1, z: 0 },
            force: Math.random() * 5 + 1,
            direction: { x: 0, y: 1, z: 0 },
            color: 0x0000ff
          }
        ];
        setForceData(newForceData);
        updateArrows(newForceData);
      }, 1000);
    }

    // 視窗大小調整
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };
    window.addEventListener('resize', handleResize);

    return () => {
      if (ws) {
        ws.close();
      }
      window.removeEventListener('resize', handleResize);
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
    };
  }, []);

  const createInitialArrows = () => {
    // 清除現有箭頭
    arrowsRef.current.forEach(arrow => {
      if (arrow) {
        sceneRef.current.remove(arrow);
      }
    });
    arrowsRef.current = [];

    // 建立三個初始箭頭
    for (let i = 0; i < 3; i++) {
      const origin = new THREE.Vector3(0, 0, 0);
      const direction = new THREE.Vector3(1, 0, 0);
      const length = 1;
      const color = [0xff0000, 0x00ff00, 0x0000ff][i];
      
      const arrowHelper = new THREE.ArrowHelper(direction, origin, length, color);
      sceneRef.current.add(arrowHelper);
      arrowsRef.current.push(arrowHelper);
    }
  };

  const updateArrows = (data) => {
    data.forEach((item, index) => {
      if (arrowsRef.current[index]) {
        const arrow = arrowsRef.current[index];
        
        // 更新位置
        arrow.position.set(item.position.x, item.position.y, item.position.z);
        
        // 更新方向和長度
        const direction = new THREE.Vector3(
          item.direction.x,
          item.direction.y,
          item.direction.z
        ).normalize();
        
        arrow.setDirection(direction);
        arrow.setLength(item.force);
        
        // 更新顏色
        arrow.setColor(item.color);
      }
    });
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const textureLoader = new THREE.TextureLoader();
        textureLoader.load(e.target.result, (texture) => {
          sceneRef.current.background = texture;
          setBackgroundImage(e.target.result);
        });
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div style={{ position: 'relative', width: '100%', height: '100vh' }}>
      {/* 控制面板 */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        zIndex: 1000,
        background: 'rgba(0,0,0,0.7)',
        color: 'white',
        padding: '15px',
        borderRadius: '8px',
        fontFamily: 'Arial, sans-serif'
      }}>
        <h3 style={{ margin: '0 0 10px 0' }}>力的分散顯示系統</h3>
        
        <div style={{ marginBottom: '10px' }}>
          <label>
            背景圖片：
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              style={{ marginLeft: '10px' }}
            />
          </label>
        </div>

        <div>
          <h4 style={{ margin: '10px 0 5px 0' }}>即時力值：</h4>
          {forceData.map((item, index) => (
            <div key={item.id} style={{ fontSize: '12px', marginBottom: '3px' }}>
              {item.deviceName || `裝置 ${item.id}`}: {item.force.toFixed(2)} N
              {item.timestamp && (
                <div style={{ fontSize: '10px', color: '#ccc' }}>
                  {new Date(item.timestamp).toLocaleTimeString()}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Three.js 渲染區域 */}
      <div ref={mountRef} style={{ width: "100%", height: "100vh" }} />
    </div>
  );
}
